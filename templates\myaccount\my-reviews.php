<?php
/**
 * My Account - My Reviews
 *
 * @package WooCustom
 */

defined('ABSPATH') || exit;

$reviews_handler = WooCustom_Reviews::instance();
$user_reviews = $reviews_handler->get_user_reviews();
$reviews_count = $reviews_handler->get_user_reviews_count();
$average_rating = $reviews_handler->get_user_average_rating();
$rating_distribution = $reviews_handler->get_user_rating_distribution();
?>

<div class="woo-custom-my-reviews">
    <div class="reviews-header">
        <h2><?php esc_html_e('Değerlendirmelerim', 'woo-custom'); ?></h2>
        
        <?php if ($reviews_count > 0) : ?>
            <div class="reviews-summary">
                <?php if ($average_rating > 0) : ?>
                    <div class="rating-overview">
                        <div class="rating-breakdown">
                            <div class="overall-rating">
                                <div class="rating-number"><?php echo esc_html($average_rating); ?></div>
                                <div class="rating-stars">
                                    <?php echo $reviews_handler->get_star_rating_html($average_rating); ?>
                                </div>
                                <div class="total-reviews">Toplam <?php echo esc_html($reviews_count); ?> Değerlendirme</div>
                            </div>

                            <div class="rating-distribution">
                                <?php for ($i = 5; $i >= 1; $i--) :
                                    $count = isset($rating_distribution[$i]) ? $rating_distribution[$i] : 0;
                                    $percentage = $reviews_count > 0 ? ($count / $reviews_count) * 100 : 0;
                                ?>
                                    <div class="rating-row">
                                        <div class="star-icon">
                                            <span class="star">☆</span>
                                            <span class="star-number"><?php echo $i; ?></span>
                                        </div>
                                        <div class="rating-bar">
                                            <div class="bar-background">
                                                <div class="bar-fill" style="width: <?php echo esc_attr($percentage); ?>%"></div>
                                            </div>
                                        </div>
                                        <div class="rating-count">
                                            <?php echo esc_html($count); ?> Değerlendirme
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <?php if (!empty($user_reviews)) : ?>
        <div class="reviews-list">
            <?php foreach ($user_reviews as $review) : 
                $comment = $review['comment'];
                $product = $review['product'];
                $rating = $review['rating'];
                $verified = $review['verified'];
                $date = $review['date'];
            ?>
                <div class="review-item">
                    <div class="review-header">
                        <div class="product-info">
                            <div class="product-thumbnail">
                                <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                    <?php echo $product->get_image('woocommerce_gallery_thumbnail'); ?>
                                </a>
                            </div>
                            
                            <div class="product-details">
                                <h4 class="product-name">
                                    <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                        <?php echo esc_html($product->get_name()); ?>
                                    </a>
                                </h4>
                                
                                <div class="review-meta">
                                    <?php if ($rating > 0) : ?>
                                        <div class="review-rating">
                                            <?php echo $reviews_handler->get_star_rating_html($rating); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="review-date">
                                        <?php echo esc_html($reviews_handler->format_review_date($date)); ?>
                                    </div>
                                    
                                    <?php if ($verified) : ?>
                                        <span class="verified-purchase">
                                            <span class="verified-icon">✓</span>
                                            <?php esc_html_e('Doğrulanmış Satın alma', 'woo-custom'); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="review-content">
                        <div class="review-text">
                            <?php echo wp_kses_post(wpautop($comment->comment_content)); ?>
                        </div>
                    </div>
                    
                    <div class="review-actions">
                        <button class="edit-review-btn"
                                data-comment-id="<?php echo esc_attr($comment->comment_ID); ?>"
                                data-product-id="<?php echo esc_attr($product->get_id()); ?>"
                                data-rating="<?php echo esc_attr($rating); ?>"
                                data-content="<?php echo esc_attr($comment->comment_content); ?>"
                                data-product-name="<?php echo esc_attr($product->get_name()); ?>">
                            <?php esc_html_e('Değerlendirmeyi Güncelle', 'woo-custom'); ?>
                        </button>
                        <button class="delete-review-btn"
                                data-comment-id="<?php echo esc_attr($comment->comment_ID); ?>"
                                data-product-name="<?php echo esc_attr($product->get_name()); ?>">
                            <?php esc_html_e('Sil', 'woo-custom'); ?>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else : ?>
        <div class="reviews-empty">
            <p><?php esc_html_e('Henüz hiç değerlendirme yazmadınız.', 'woo-custom'); ?></p>
            <p><?php esc_html_e('Ürün satın alın ve deneyimlerinizi diğer müşterilerle paylaşın!', 'woo-custom'); ?></p>
            <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="button">
                <?php esc_html_e('Alışverişe Başla', 'woo-custom'); ?>
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Değerlendirme Güncelleme Popup -->
<div id="edit-review-modal" class="review-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3><?php esc_html_e('Değerlendirmeyi Güncelle', 'woo-custom'); ?></h3>
            <button class="modal-close">&times;</button>
        </div>

        <div class="modal-body">
            <form id="edit-review-form">
                <div class="form-group">
                    <label><?php esc_html_e('Ürün:', 'woo-custom'); ?></label>
                    <div class="product-info-display">
                        <span id="modal-product-name"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label><?php esc_html_e('Puanınız:', 'woo-custom'); ?></label>
                    <div class="rating-input">
                        <div class="star-rating-input">
                            <?php for ($i = 5; $i >= 1; $i--) : ?>
                                <input type="radio" name="rating" value="<?php echo $i; ?>" id="star<?php echo $i; ?>">
                                <label for="star<?php echo $i; ?>" class="star-label">★</label>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="review-content"><?php esc_html_e('Değerlendirmeniz:', 'woo-custom'); ?></label>
                    <textarea id="review-content" name="content" rows="5" placeholder="<?php esc_attr_e('Ürün hakkındaki düşüncelerinizi paylaşın...', 'woo-custom'); ?>"></textarea>
                </div>

                <input type="hidden" id="comment-id" name="comment_id" value="">
                <input type="hidden" id="product-id" name="product_id" value="">
            </form>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary modal-cancel">
                <?php esc_html_e('İptal', 'woo-custom'); ?>
            </button>
            <button type="button" class="btn btn-primary" id="save-review">
                <?php esc_html_e('Güncelle', 'woo-custom'); ?>
            </button>
        </div>
    </div>
</div>

<style>
.woo-custom-my-reviews .reviews-header {
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
}

.woo-custom-my-reviews .reviews-header h2 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.reviews-summary {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-top: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}



.rating-overview {
    margin-top: 20px;
}

.rating-breakdown {
    display: flex;
    gap: 40px;
    align-items: flex-start;
    background: #fff;
    border-radius: 8px;
}

.overall-rating {
    text-align: center;
    min-width: 120px;
}

.rating-number {
    font-size: 48px;
    font-weight: 700;
    color: #333;
    line-height: 1;
    margin-bottom: 8px;
}

.rating-stars {
    margin-bottom: 8px;
    display: flex;
    justify-content: center;
}

.rating-stars .star-rating {
    font-size: 18px;
    text-align: left;
}

.total-reviews {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.rating-distribution {
    flex: 1;
    max-width: 400px;
}

.rating-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    font-size: 14px;
}

.star-icon {
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 30px;
}

.star {
    color: #ffc107;
    font-size: 16px;
}

.star-number {
    font-weight: 500;
    color: #333;
}

.rating-bar {
    flex: 1;
    max-width: 200px;
}

.bar-background {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: #007cba;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.rating-count {
    min-width: 80px;
    text-align: right;
    color: #666;
    font-size: 13px;
}

.reviews-list {
    margin-top: 30px;
}

.review-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
}

.review-header {
    margin-bottom: 15px;
}

.product-info {
    display: flex;
    gap: 15px;
}

.product-thumbnail {
    flex-shrink: 0;
}

.product-thumbnail img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
}

.product-details {
    flex: 1;
}

.product-name {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.product-name a {
    text-decoration: none;
    color: #333;
}

.product-name a:hover {
    color: #0073aa;
}

.review-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.woo-custom-star-rating {
    display: flex;
    gap: 2px;
}

.woo-custom-star-rating .star {
    color: #ddd;
    font-size: 16px;
}

.woo-custom-star-rating .star.filled {
    color: #ffc107;
}

.review-date {
    color: #666;
    font-size: 14px;
}

.verified-purchase {
    background: #7ad03a;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.verified-icon {
    font-weight: bold;
}

.review-content {
    margin-bottom: 15px;
}

.review-text {
    line-height: 1.6;
    color: #333;
}

.review-actions {
    border-top: 1px solid #eee;
    padding-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.edit-review-btn {
    color: #333;
    text-decoration: none;
    font-size: 13px;
    padding: 6px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    background: #f8f9fa;
    cursor: pointer;
    font-family: inherit;
    font-weight: 500;
}

.edit-review-btn:hover {
    background: #e9ecef;
    border-color: #d0d0d0;
    color: #333;
}

/* Modal Styles */
.review-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f1f3f4;
    color: #333;
}

.modal-body {
    padding: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.product-info-display {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
    font-weight: 500;
    color: #495057;
}

.star-rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 4px;
}

.star-rating-input input[type="radio"] {
    display: none;
}

.star-label {
    font-size: 24px;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s ease;
    user-select: none;
}

.star-rating-input input[type="radio"]:checked ~ .star-label,
.star-rating-input .star-label:hover,
.star-rating-input .star-label:hover ~ .star-label {
    color: #ffc107;
}

#review-content {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    min-height: 100px;
    transition: border-color 0.3s ease;
}

#review-content:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.modal-footer {
    padding: 16px 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-primary {
    background: #007cba;
    color: white;
}

.btn-primary:hover {
    background: #005a87;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.reviews-empty {
    text-align: center;
    padding: 40px 20px;
    background: #f8f8f8;
    border-radius: 5px;
}

.reviews-empty p {
    margin-bottom: 15px;
    color: #666;
}

.reviews-empty .button {
    margin-top: 20px;
}

@media (max-width: 768px) {
    .summary-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .product-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .product-thumbnail {
        align-self: center;
    }
    
    .review-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .average-rating {
        flex-direction: column;
        gap: 5px;
    }

    .rating-breakdown {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .overall-rating {
        min-width: auto;
    }

    .rating-distribution {
        max-width: none;
    }

    .rating-row {
        justify-content: space-between;
        gap: 8px;
    }

    .rating-bar {
        max-width: 120px;
    }

    .rating-count {
        min-width: 60px;
        font-size: 12px;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 8px;
    }

    .btn {
        width: 100%;
    }

    .review-actions {
        flex-direction: column;
        gap: 8px;
    }

    .edit-review-btn {
        text-align: center;
        width: 100%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('edit-review-modal');
    const editBtns = document.querySelectorAll('.edit-review-btn');
    const closeBtn = document.querySelector('.modal-close');
    const cancelBtn = document.querySelector('.modal-cancel');
    const saveBtn = document.getElementById('save-review');
    const overlay = document.querySelector('.modal-overlay');

    // Modal açma
    editBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const commentId = this.getAttribute('data-comment-id');
            const productId = this.getAttribute('data-product-id');
            const rating = this.getAttribute('data-rating');
            const content = this.getAttribute('data-content');
            const productName = this.getAttribute('data-product-name');

            // Form verilerini doldur
            document.getElementById('comment-id').value = commentId;
            document.getElementById('product-id').value = productId;
            document.getElementById('modal-product-name').textContent = productName;
            document.getElementById('review-content').value = content;

            // Rating'i seç
            const ratingInput = document.querySelector(`input[name="rating"][value="${rating}"]`);
            if (ratingInput) {
                ratingInput.checked = true;
            }

            // Modal'ı göster
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        });
    });

    // Modal kapama fonksiyonları
    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';

        // Formu temizle
        document.getElementById('edit-review-form').reset();
    }

    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    overlay.addEventListener('click', closeModal);

    // ESC tuşu ile kapama
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'flex') {
            closeModal();
        }
    });

    // Yıldız rating interaktivitesi
    const starInputs = document.querySelectorAll('.star-rating-input input[type="radio"]');
    const starLabels = document.querySelectorAll('.star-label');

    starLabels.forEach((label, index) => {
        label.addEventListener('mouseenter', function() {
            const value = this.getAttribute('for').replace('star', '');
            highlightStars(value);
        });

        label.addEventListener('mouseleave', function() {
            const checkedInput = document.querySelector('.star-rating-input input[type="radio"]:checked');
            if (checkedInput) {
                highlightStars(checkedInput.value);
            } else {
                clearStars();
            }
        });
    });

    function highlightStars(rating) {
        starLabels.forEach((label, index) => {
            const starValue = label.getAttribute('for').replace('star', '');
            if (starValue <= rating) {
                label.style.color = '#ffc107';
            } else {
                label.style.color = '#ddd';
            }
        });
    }

    function clearStars() {
        starLabels.forEach(label => {
            label.style.color = '#ddd';
        });
    }

    // Form gönderme
    saveBtn.addEventListener('click', function() {
        const commentId = document.getElementById('comment-id').value;
        const productId = document.getElementById('product-id').value;
        const rating = document.querySelector('input[name="rating"]:checked');
        const content = document.getElementById('review-content').value.trim();

        // Validasyon
        if (!rating) {
            return;
        }

        if (!content) {
            return;
        }

        // Butonu devre dışı bırak
        saveBtn.disabled = true;
        saveBtn.textContent = 'Güncelleniyor...';

        // AJAX isteği
        const formData = new FormData();
        formData.append('action', 'update_review');
        formData.append('comment_id', commentId);
        formData.append('product_id', productId);
        formData.append('rating', rating.value);
        formData.append('content', content);
        formData.append('nonce', woo_custom_ajax.nonce);

        fetch(woo_custom_ajax.ajax_url, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeModal();
                // Sayfayı yenile
                window.location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
        })
        .finally(() => {
            // Butonu tekrar aktif et
            saveBtn.disabled = false;
            saveBtn.textContent = 'Güncelle';
        });
    });

    // Değerlendirme silme işlemi
    const deleteBtns = document.querySelectorAll('.delete-review-btn');

    deleteBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const commentId = this.getAttribute('data-comment-id');
            const productName = this.getAttribute('data-product-name');

            // Onay mesajı göster
            if (confirm(`"${productName}" ürünü için yazdığınız değerlendirmeyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`)) {
                // Butonu devre dışı bırak
                this.disabled = true;
                this.textContent = 'Siliniyor...';

                // AJAX isteği
                const formData = new FormData();
                formData.append('action', 'delete_review');
                formData.append('comment_id', commentId);
                formData.append('nonce', woo_custom_ajax.nonce);

                fetch(woo_custom_ajax.ajax_url, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Başarılı mesaj göster
                        alert(data.data.message);
                        // Sayfayı yenile
                        window.location.reload();
                    } else {
                        // Hata mesajı göster
                        alert(data.data.message || 'Bir hata oluştu.');
                        // Butonu tekrar aktif et
                        this.disabled = false;
                        this.textContent = 'Sil';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Bir hata oluştu. Lütfen tekrar deneyin.');
                    // Butonu tekrar aktif et
                    this.disabled = false;
                    this.textContent = 'Sil';
                });
            }
        });
    });
});
</script>

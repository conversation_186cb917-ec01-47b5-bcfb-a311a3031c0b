/**
 * Address Cards Styling for WooCommerce My Account
 */

/* Font Awesome CDN Import */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

/* Address Cards Main Container */
.woo-custom-addresses-header {
    margin-bottom: 30px;
    text-align: left;
    padding: 0 0 20px 0;
}

.addresses-title {
    color: #2c3e50;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.addresses-description {
    color: #6c757d;
    font-size: 16px;
    margin: 0;
    line-height: 1.6;
    text-align: left;
}

.woo-custom-addresses-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 30px;
    padding: 0 10px;
}

/* Address Card Styling */
.woo-custom-address-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid #e8ecf0;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    min-height: 280px;
}

.woo-custom-address-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Card Header */
.address-card-header {
    display: flex;
    align-items: center;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9ff, #f3f4f6);
    border-bottom: 1px solid #e8ecf0;
    position: relative;
}

.address-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 24px;
    position: relative;
    overflow: hidden;
}

.address-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    border-radius: 16px;
}

.billing-address .address-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.shipping-address .address-icon {
    background: linear-gradient(135deg, #764ba2, #667eea);
    color: white;
    box-shadow: 0 8px 25px rgba(118, 75, 162, 0.3);
}

.address-title-section {
    flex: 1;
}

.address-title {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
    letter-spacing: -0.5px;
}

.address-type {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
    background: rgba(108, 117, 125, 0.1);
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
}

.address-actions {
    margin-left: auto;
}

.edit-address-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white !important;
    text-decoration: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.edit-address-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.edit-address-btn:hover::before {
    left: 100%;
}

.edit-address-btn:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
    transform: scale(1.05);
    text-decoration: none;
    color: white !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Card Content */
.address-card-content {
    padding: 25px;
    min-height: 140px;
    display: flex;
    align-items: center;
}

.address-details {
    color: #495057;
    line-height: 1.8;
    font-size: 15px;
    width: 100%;
}

.address-details br {
    margin-bottom: 8px;
}

/* Empty State */
.address-empty {
    text-align: center;
    padding: 30px 20px;
    width: 100%;
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e9ecef, #f8f9fa);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
    color: #6c757d;
    position: relative;
    overflow: hidden;
}

.empty-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.5), rgba(255,255,255,0.2));
    border-radius: 50%;
}

.empty-text {
    color: #6c757d;
    margin-bottom: 25px;
    font-size: 16px;
    line-height: 1.6;
}

.add-address-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 14px 24px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white !important;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.add-address-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.add-address-btn:hover::before {
    left: 100%;
}

.add-address-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: scale(1.05);
    text-decoration: none;
    color: white !important;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Card Footer */
.address-card-footer {
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-top: 1px solid #e8ecf0;
}

.address-status {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #28a745;
    font-size: 14px;
    font-weight: 600;
}

.address-status i {
    font-size: 16px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .woo-custom-addresses-container {
        grid-template-columns: 1fr;
        gap: 25px;
        padding: 0 5px;
    }
    
    .address-card-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 20px;
    }
    
    .address-title-section {
        order: 2;
    }
    
    .address-actions {
        order: 3;
        margin-left: 0;
    }
    
    .address-icon {
        order: 1;
        margin-right: 0;
    }
    
    .addresses-title {
        font-size: 28px;
    }
    
    .address-card-content {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .woo-custom-addresses-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .addresses-title {
        font-size: 24px;
    }
    
    .addresses-description {
        font-size: 14px;
    }
    
    .address-card-content {
        padding: 15px;
        min-height: 120px;
    }
    
    .address-card-header {
        padding: 15px;
    }
    
    .address-card-footer {
        padding: 15px;
    }
    
    .edit-address-btn,
    .add-address-btn {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .address-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .empty-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
}

/* Animation for card entrance */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.woo-custom-address-card {
    animation: fadeInUp 0.6s ease-out;
}

.woo-custom-address-card:nth-child(2) {
    animation-delay: 0.1s;
}

/* Loading state */
.address-card-loading {
    position: relative;
    overflow: hidden;
}

.address-card-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

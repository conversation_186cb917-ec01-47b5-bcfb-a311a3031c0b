<?php
/**
 * WooCommerce My Account customizations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCustom_Account {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add custom menu items to My Account
        add_filter('woocommerce_account_menu_items', array($this, 'add_account_menu_items'));

        // Add custom endpoints
        add_action('init', array($this, 'add_endpoints'));

        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));

        // Handle endpoint content
        add_action('woocommerce_account_wishlist_endpoint', array($this, 'wishlist_endpoint_content'));
        add_action('woocommerce_account_my-reviews_endpoint', array($this, 'my_reviews_endpoint_content'));

        // Set page titles
        add_filter('woocommerce_endpoint_wishlist_title', array($this, 'wishlist_endpoint_title'));
        add_filter('woocommerce_endpoint_my-reviews_title', array($this, 'my_reviews_endpoint_title'));

        // Add dashboard customizations
        add_action('woocommerce_account_dashboard', array($this, 'add_dashboard_summary'), 5);
    }
    
    /**
     * Add custom menu items to My Account
     */
    public function add_account_menu_items($items) {
        // Insert wishlist after orders
        $new_items = array();
        foreach ($items as $key => $item) {
            $new_items[$key] = $item;
            if ($key === 'orders') {
                $new_items['wishlist'] = __('İstek Listesi', 'woo-custom'); // Turkish: Wishlist
            }
        }

        // Insert my-reviews before edit-account
        $final_items = array();
        foreach ($new_items as $key => $item) {
            if ($key === 'edit-account') {
                $final_items['my-reviews'] = __('Değerlendirmelerim', 'woo-custom'); // Turkish: My Reviews
            }
            $final_items[$key] = $item;
        }
        
        return $final_items;
    }
    
    /**
     * Add custom endpoints
     */
    public function add_endpoints() {
        add_rewrite_endpoint('wishlist', EP_ROOT | EP_PAGES);
        add_rewrite_endpoint('my-reviews', EP_ROOT | EP_PAGES);
    }
    
    /**
     * Add query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'wishlist';
        $vars[] = 'my-reviews';
        return $vars;
    }
    
    /**
     * Wishlist endpoint content
     */
    public function wishlist_endpoint_content() {
        wc_get_template('myaccount/wishlist.php', array(), '', WOO_CUSTOM_PLUGIN_DIR . 'templates/');
    }
    
    /**
     * My Reviews endpoint content
     */
    public function my_reviews_endpoint_content() {
        wc_get_template('myaccount/my-reviews.php', array(), '', WOO_CUSTOM_PLUGIN_DIR . 'templates/');
    }
    
    /**
     * Wishlist endpoint title
     */
    public function wishlist_endpoint_title($title) {
        return __('İstek Listesi', 'woo-custom');
    }

    /**
     * My Reviews endpoint title
     */
    public function my_reviews_endpoint_title($title) {
        return __('Değerlendirmelerim', 'woo-custom');
    }

    /**
     * Add dashboard summary section
     */
    public function add_dashboard_summary() {
        $current_user = wp_get_current_user();
        $user_id = get_current_user_id();

        // Get user statistics
        $order_count = $this->get_user_order_count($user_id);
        $wishlist_count = $this->get_user_wishlist_count($user_id);
        $review_count = $this->get_user_review_count($user_id);
        $recent_orders = $this->get_recent_orders($user_id, 3);

        ?>
        <div class="woo-custom-dashboard-summary">
            <!-- Welcome Section -->
            <div class="woo-custom-welcome-section">
                <h2 class="woo-custom-welcome-title">
                    <?php printf(__('Hoşgeldin, %s!', 'woo-custom'), esc_html($current_user->display_name)); ?>
                </h2>
                <p class="woo-custom-welcome-subtitle">
                    <?php _e('Hesap özetinizi ve son aktivitelerinizi buradan takip edebilirsiniz.', 'woo-custom'); ?>
                </p>
            </div>

            <!-- Summary Cards -->
            <div class="woo-custom-summary-cards">
                <!-- Orders Summary -->
                <div class="woo-custom-summary-card">
                    <div class="summary-card-content">
                        <h3><?php _e('Siparişlerim', 'woo-custom'); ?></h3>
                        <div class="summary-card-number"><?php echo esc_html($order_count); ?></div>
                        <p><?php _e('Toplam sipariş', 'woo-custom'); ?></p>
                        <a href="<?php echo esc_url(wc_get_endpoint_url('orders')); ?>" class="summary-card-link">
                            <?php _e('Tümünü Gör', 'woo-custom'); ?>
                        </a>
                    </div>
                </div>

                <!-- Wishlist Summary -->
                <div class="woo-custom-summary-card">
                    <div class="summary-card-content">
                        <h3><?php _e('İstek Listem', 'woo-custom'); ?></h3>
                        <div class="summary-card-number"><?php echo esc_html($wishlist_count); ?></div>
                        <p><?php _e('Kayıtlı ürün', 'woo-custom'); ?></p>
                        <a href="<?php echo esc_url(wc_get_endpoint_url('wishlist')); ?>" class="summary-card-link">
                            <?php _e('Tümünü Gör', 'woo-custom'); ?>
                        </a>
                    </div>
                </div>

                <!-- Reviews Summary -->
                <div class="woo-custom-summary-card">
                    <div class="summary-card-content">
                        <h3><?php _e('Değerlendirmelerim', 'woo-custom'); ?></h3>
                        <div class="summary-card-number"><?php echo esc_html($review_count); ?></div>
                        <p><?php _e('Yaptığım değerlendirme', 'woo-custom'); ?></p>
                        <a href="<?php echo esc_url(wc_get_endpoint_url('my-reviews')); ?>" class="summary-card-link">
                            <?php _e('Tümünü Gör', 'woo-custom'); ?>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Orders Section -->
            <?php if (!empty($recent_orders)) : ?>
            <div class="woo-custom-recent-orders">
                <h3><?php _e('Son Siparişlerim', 'woo-custom'); ?></h3>
                <div class="recent-orders-list">
                    <?php foreach ($recent_orders as $order) : ?>
                        <div class="recent-order-item">
                            <div class="order-info">
                                <span class="order-number">#<?php echo esc_html($order->get_order_number()); ?></span>
                                <span class="order-date"><?php echo esc_html($order->get_date_created()->date_i18n('d.m.Y')); ?></span>
                            </div>
                            <div class="order-status">
                                <span class="status-badge status-<?php echo esc_attr($order->get_status()); ?>">
                                    <?php echo esc_html(wc_get_order_status_name($order->get_status())); ?>
                                </span>
                            </div>
                            <div class="order-total">
                                <?php echo wp_kses_post($order->get_formatted_order_total()); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Get user order count
     */
    private function get_user_order_count($user_id) {
        $orders = wc_get_orders(array(
            'customer' => $user_id,
            'limit' => -1,
            'return' => 'ids',
            'status' => array('wc-pending', 'wc-processing', 'wc-on-hold', 'wc-completed', 'wc-cancelled', 'wc-refunded', 'wc-failed')
        ));
        return count($orders);
    }

    /**
     * Get user wishlist count
     */
    private function get_user_wishlist_count($user_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'woo_custom_wishlist';

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
            $user_id
        ));

        return intval($count);
    }

    /**
     * Get user review count
     */
    private function get_user_review_count($user_id) {
        $comments = get_comments(array(
            'user_id' => $user_id,
            'post_type' => 'product',
            'count' => true
        ));
        return intval($comments);
    }

    /**
     * Get recent orders
     */
    private function get_recent_orders($user_id, $limit = 3) {
        return wc_get_orders(array(
            'customer' => $user_id,
            'limit' => $limit,
            'orderby' => 'date',
            'order' => 'DESC',
            'status' => array('wc-pending', 'wc-processing', 'wc-on-hold', 'wc-completed', 'wc-cancelled', 'wc-refunded', 'wc-failed')
        ));
    }
}

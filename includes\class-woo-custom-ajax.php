<?php
/**
 * AJAX functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCustom_Ajax {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX actions for logged in users
        add_action('wp_ajax_woo_custom_toggle_wishlist', array($this, 'toggle_wishlist'));
        
        // AJAX actions for non-logged in users (redirect to login)
        add_action('wp_ajax_nopriv_woo_custom_toggle_wishlist', array($this, 'require_login'));
    }
    
    /**
     * Toggle product in wishlist
     */
    public function toggle_wishlist() {
        // Enable error logging for debugging
        $debug_mode = defined('WP_DEBUG') && WP_DEBUG;

        try {
            // Verify nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'woo_custom_nonce')) {
                if ($debug_mode) {
                    error_log('Woo Custom: Nonce verification failed. Received nonce: ' . (isset($_POST['nonce']) ? $_POST['nonce'] : 'none'));
                }
                wp_send_json_error(array(
                    'message' => __('Güvenlik kontrolü başarısız', 'woo-custom'),
                    'debug' => $debug_mode ? 'Nonce verification failed' : null
                ));
            }

            // Check if user is logged in
            if (!is_user_logged_in()) {
                if ($debug_mode) {
                    error_log('Woo Custom: User not logged in');
                }
                wp_send_json_error(array(
                    'message' => __('İstek listesini kullanmak için giriş yapmalısınız', 'woo-custom'),
                    // redirect parametresi kaldirildi - yonlendirme yapilmayacak
                    'debug' => $debug_mode ? 'User not logged in' : null
                ));
            }

            // Get product ID
            $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
            if (!$product_id) {
                if ($debug_mode) {
                    error_log('Woo Custom: Invalid product ID. Received: ' . print_r($_POST, true));
                }
                wp_send_json_error(array(
                    'message' => __('Geçersiz ürün ID\'si', 'woo-custom'),
                    'debug' => $debug_mode ? 'Product ID: ' . $product_id : null
                ));
            }

            // Check if product exists
            $product = wc_get_product($product_id);
            if (!$product || !$product->exists()) {
                if ($debug_mode) {
                    error_log('Woo Custom: Product not found. Product ID: ' . $product_id);
                }
                wp_send_json_error(array(
                    'message' => __('Ürün bulunamadı', 'woo-custom'),
                    'debug' => $debug_mode ? 'Product ID: ' . $product_id : null
                ));
            }

            // Check if wishlist table exists and create if needed
            $main_plugin = WooCustom::instance();
            if (!$main_plugin->ensure_table_exists()) {
                if ($debug_mode) {
                    error_log('Woo Custom: Wishlist table could not be created or accessed');
                }
                wp_send_json_error(array(
                    'message' => __('Wishlist functionality is not available', 'woo-custom'),
                    'debug' => $debug_mode ? 'Table creation failed' : null
                ));
            }

            $wishlist = WooCustom_Wishlist::instance();
            $user_id = get_current_user_id();

            // Check if product is in wishlist
            $is_in_wishlist = $wishlist->is_product_in_wishlist($product_id, $user_id);

            if ($is_in_wishlist) {
                // Remove from wishlist
                $result = $wishlist->remove_from_wishlist($product_id, $user_id);
                if ($result) {
                    if ($debug_mode) {
                        error_log('Woo Custom: Successfully removed product ' . $product_id . ' from wishlist for user ' . $user_id);
                    }
                    wp_send_json_success(array(
                        'action' => 'removed',
                        'message' => __('İstek listesinden çıkarıldı', 'woo-custom'),
                        'count' => $wishlist->get_wishlist_count($user_id)
                    ));
                } else {
                    if ($debug_mode) {
                        error_log('Woo Custom: Failed to remove product ' . $product_id . ' from wishlist for user ' . $user_id . '. DB Error: ' . $wpdb->last_error);
                    }
                    wp_send_json_error(array(
                        'message' => __('İstek listesinden çıkarma başarısız', 'woo-custom'),
                        'debug' => $debug_mode ? 'DB Error: ' . $wpdb->last_error : null
                    ));
                }
            } else {
                // Add to wishlist
                $result = $wishlist->add_to_wishlist($product_id, $user_id);
                if ($result) {
                    if ($debug_mode) {
                        error_log('Woo Custom: Successfully added product ' . $product_id . ' to wishlist for user ' . $user_id);
                    }
                    wp_send_json_success(array(
                        'action' => 'added',
                        'message' => __('İstek listesine eklendi', 'woo-custom'),
                        'count' => $wishlist->get_wishlist_count($user_id)
                    ));
                } else {
                    if ($debug_mode) {
                        error_log('Woo Custom: Failed to add product ' . $product_id . ' to wishlist for user ' . $user_id . '. DB Error: ' . $wpdb->last_error);
                    }
                    wp_send_json_error(array(
                        'message' => __('İstek listesine ekleme başarısız', 'woo-custom'),
                        'debug' => $debug_mode ? 'DB Error: ' . $wpdb->last_error : null
                    ));
                }
            }
        } catch (Exception $e) {
            if ($debug_mode) {
                error_log('Woo Custom: Exception in toggle_wishlist: ' . $e->getMessage());
            }
            wp_send_json_error(array(
                'message' => __('Beklenmeyen bir hata oluştu', 'woo-custom'),
                'debug' => $debug_mode ? $e->getMessage() : null
            ));
        }
    }
    
    /**
     * Require login for non-logged in users
     */
    public function require_login() {
        wp_send_json_error(array(
            'message' => __('İstek listesini kullanmak için giriş yapmalısınız', 'woo-custom')
            // redirect parametresi kaldirildi - yonlendirme yapilmayacak
        ));
    }
}

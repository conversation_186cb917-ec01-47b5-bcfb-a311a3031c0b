<?php
/**
 * Profile Photos Section for WooCommerce My Account
 * Based on Tutor LMS profile photo editor
 */

defined('ABSPATH') || exit;
?>

<div class="woo-custom-profile-photos-section">
    <h3><?php esc_html_e('Profil ve <PERSON> Fotoğrafı', 'woo-custom'); ?></h3>

    <div id="woo_custom_profile_cover_photo_editor">

        <input id="woo_custom_photo_dialogue_box" type="file" accept=".png,.jpg,.jpeg" style="display: none;"/>
        <input type="hidden" class="upload_max_filesize" value="<?php echo esc_attr($max_filesize); ?>">

        <!-- Cover Photo Area -->
        <div id="woo_custom_cover_area"
             class="woo-custom-cover-area"
             data-fallback="<?php echo esc_attr($cover_placeholder); ?>"
             style="background-image:url(<?php echo esc_url($cover_photo_src); ?>)">

            <span class="woo_custom_cover_deleter" style="<?php echo !$cover_photo_id ? 'display:none;' : ''; ?>">
                <span class="woo-custom-delete-icon">×</span>
            </span>

            <div class="woo_custom_overlay">
                <button type="button" class="woo_custom_cover_uploader woo-custom-btn woo-custom-btn-primary">
                    <i class="woo-custom-icon-camera"></i>
                    <span><?php echo $cover_photo_id ? esc_html__('Kapak Fotoğrafını Güncelle', 'woo-custom') : esc_html__('Kapak Fotoğrafı Yükle', 'woo-custom'); ?></span>
                </button>
            </div>
        </div>

        <!-- Photo Info Area -->
        <div id="woo_custom_photo_meta_area" class="woo-custom-photo-meta">
            <div class="woo-custom-photo-info">
                <span><?php esc_html_e('Profil Fotoğrafı Boyutu', 'woo-custom'); ?>: <strong>200x200</strong> <?php esc_html_e('piksel', 'woo-custom'); ?></span>
                <span><?php esc_html_e('Kapak Fotoğrafı Boyutu', 'woo-custom'); ?>: <strong>700x430</strong> <?php esc_html_e('piksel', 'woo-custom'); ?></span>
            </div>
            <span class="woo-custom-loader-area" style="display: none;"><?php esc_html_e('Kaydediliyor...', 'woo-custom'); ?></span>
        </div>

        <!-- Profile Photo Area -->
        <div id="woo_custom_profile_area"
             class="woo-custom-profile-area"
             data-fallback="<?php echo esc_attr($profile_placeholder); ?>"
             style="background-image:url(<?php echo esc_url($profile_photo_src); ?>)">

            <div class="woo_custom_overlay">
                <i class="woo-custom-icon-camera"></i>
            </div>
        </div>

        <!-- Profile Photo Options -->
        <div id="woo_custom_pp_option" class="woo-custom-pp-options" style="display: none;">
            <div class="woo-custom-up-arrow">
                <i></i>
            </div>

            <span class="woo_custom_pp_uploader woo-custom-profile-uploader">
                <i class="woo-custom-icon-upload"></i> <?php esc_html_e('Fotoğraf Yükle', 'woo-custom'); ?>
            </span>

            <span class="woo_custom_pp_deleter woo-custom-profile-uploader" style="<?php echo !$profile_photo_id ? 'display:none;' : ''; ?>">
                <i class="woo-custom-icon-delete"></i> <?php esc_html_e('Sil', 'woo-custom'); ?>
            </span>
        </div>

    </div>
</div>
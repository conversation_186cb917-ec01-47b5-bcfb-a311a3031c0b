<?php
/**
 * WooCommerce Featured Video functionality
 * 
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WooCustom Featured Video Class
 */
class WooCustom_Featured_Video {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Admin hooks for product edit page
        add_action('woocommerce_product_options_general_product_data', array($this, 'add_video_fields'));
        add_action('woocommerce_admin_process_product_object', array($this, 'save_video_fields'), 10, 1);

        // Frontend hooks for displaying video in gallery
        add_filter('woocommerce_single_product_image_thumbnail_html', array($this, 'add_video_to_gallery'), 10, 2);

        // Admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));

        // Frontend scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'frontend_enqueue_scripts'));
    }
    
    /**
     * Add video fields to product general tab
     */
    public function add_video_fields() {
        global $post;
        
        echo '<div class="options_group">';
        
        // Video URL field
        woocommerce_wp_text_input(array(
            'id' => '_woo_custom_video_url',
            'label' => __('Featured Video URL', 'woo-custom'),
            'placeholder' => __('YouTube veya Vimeo video URL\'si girin', 'woo-custom'),
            'desc_tip' => true,
            'description' => __('Ürün sayfasında gösterilecek video URL\'sini girin. YouTube ve Vimeo desteklenir.', 'woo-custom'),
            'type' => 'url'
        ));
        
        // Aspect ratio field
        woocommerce_wp_select(array(
            'id' => '_woo_custom_video_aspect_ratio',
            'label' => __('Video En Boy Oranı', 'woo-custom'),
            'desc_tip' => true,
            'description' => __('Video için en boy oranını seçin.', 'woo-custom'),
            'options' => array(
                '16:9' => __('16:9 (Geniş ekran)', 'woo-custom'),
                '4:3' => __('4:3 (Standart)', 'woo-custom'),
            ),
            'value' => get_post_meta($post->ID, '_woo_custom_video_aspect_ratio', true) ?: '16:9'
        ));
        
        echo '</div>';
    }
    
    /**
     * Save video fields when product is saved
     */
    public function save_video_fields($product) {
        // Save video URL
        if (isset($_POST['_woo_custom_video_url'])) {
            $video_url = sanitize_url($_POST['_woo_custom_video_url']);
            $product->update_meta_data('_woo_custom_video_url', $video_url);
        }
        
        // Save aspect ratio
        if (isset($_POST['_woo_custom_video_aspect_ratio'])) {
            $aspect_ratio = sanitize_text_field($_POST['_woo_custom_video_aspect_ratio']);
            $product->update_meta_data('_woo_custom_video_aspect_ratio', $aspect_ratio);
        }
    }
    
    /**
     * Add video to product gallery (replaces featured image)
     */
    public function add_video_to_gallery($html, $post_thumbnail_id) {
        global $product;

        if (!$product) {
            return $html;
        }

        // Only replace the first image (featured image)
        static $counter = 0;
        if ($counter > 0 || $post_thumbnail_id !== $product->get_image_id()) {
            return $html;
        }

        $video_url = $product->get_meta('_woo_custom_video_url');
        $aspect_ratio = $product->get_meta('_woo_custom_video_aspect_ratio') ?: '16:9';

        if (empty($video_url)) {
            return $html;
        }

        $embed_url = $this->get_video_embed_url($video_url);

        if (!$embed_url) {
            return $html;
        }

        $counter++;

        // Get thumbnail for gallery navigation
        $thumbnail_url = $this->get_video_thumbnail($video_url);
        $aspect_class = $aspect_ratio === '4:3' ? 'aspect-4-3' : 'aspect-16-9';

        // Create video HTML that integrates with WooCommerce gallery
        ob_start();
        ?>
        <div class="woocommerce-product-gallery__image woo-custom-featured-video-gallery" data-thumb="<?php echo esc_url($thumbnail_url); ?>">
            <div class="woo-custom-featured-video <?php echo esc_attr($aspect_class); ?>">
                <div class="video-container">
                    <iframe
                        src="<?php echo esc_url($embed_url); ?>"
                        frameborder="0"
                        allowfullscreen
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                    </iframe>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Convert video URL to embed URL
     */
    private function get_video_embed_url($url) {
        // YouTube URL patterns
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
            return 'https://www.youtube.com/embed/' . $matches[1] . '?rel=0&showinfo=0&enablejsapi=1';
        }

        // Vimeo URL patterns
        if (preg_match('/vimeo\.com\/(\d+)/', $url, $matches)) {
            return 'https://player.vimeo.com/video/' . $matches[1];
        }

        return false;
    }

    /**
     * Get video thumbnail URL
     */
    private function get_video_thumbnail($url) {
        // YouTube thumbnail
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
            return 'https://img.youtube.com/vi/' . $matches[1] . '/maxresdefault.jpg';
        }

        // Vimeo thumbnail (simplified - in real implementation you'd need API call)
        if (preg_match('/vimeo\.com\/(\d+)/', $url, $matches)) {
            // For now, return a placeholder. In production, you'd use Vimeo API
            return WOO_CUSTOM_PLUGIN_URL . 'assets/images/video-placeholder.svg';
        }

        return WOO_CUSTOM_PLUGIN_URL . 'assets/images/video-placeholder.svg';
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        // Only load on product edit pages
        if ($hook !== 'post.php' && $hook !== 'post-new.php') {
            return;
        }

        global $post;
        if (!$post || $post->post_type !== 'product') {
            return;
        }

        wp_enqueue_style(
            'woo-custom-featured-video-admin',
            WOO_CUSTOM_PLUGIN_URL . 'assets/css/featured-video-admin.css',
            array(),
            WOO_CUSTOM_VERSION
        );
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function frontend_enqueue_scripts() {
        if (is_product()) {
            wp_enqueue_style(
                'woo-custom-featured-video-frontend',
                WOO_CUSTOM_PLUGIN_URL . 'assets/css/featured-video-frontend.css',
                array(),
                WOO_CUSTOM_VERSION
            );

            wp_enqueue_script(
                'woo-custom-featured-video-frontend',
                WOO_CUSTOM_PLUGIN_URL . 'assets/js/featured-video-frontend.js',
                array('jquery'),
                WOO_CUSTOM_VERSION,
                true
            );
        }
    }
}
